import React, { useState, useEffect } from "react";
import {
  Input,
  Button,
  Select,
  DatePicker,
  Rate,
  Tag,
  Modal,
  Drawer,
  message,
} from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  ClockCircleOutlined,
  TagOutlined,
  AppstoreOutlined,
  BarsOutlined,
} from "@ant-design/icons";
import './App.css';

// API配置
const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'https://5000-i9pyf58rateboa32ofaui-32747925.manusvm.computer/api';

// API调用函数
const api = {
  // 获取书签列表
  getBookmarks: async (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.tag && params.tag !== 'all') queryParams.append('tag', params.tag);
    if (params.search) queryParams.append('search', params.search);
    if (params.sort) queryParams.append('sort', params.sort);
    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    
    const response = await fetch(`${API_BASE_URL}/bookmarks?${queryParams}`);
    return response.json();
  },

  // 创建书签
  createBookmark: async (data) => {
    const response = await fetch(`${API_BASE_URL}/bookmarks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // 更新书签
  updateBookmark: async (id, data) => {
    const response = await fetch(`${API_BASE_URL}/bookmarks/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    return response.json();
  },

  // 删除书签
  deleteBookmark: async (id) => {
    const response = await fetch(`${API_BASE_URL}/bookmarks/${id}`, {
      method: 'DELETE',
    });
    return response.json();
  },

  // 获取标签
  getTags: async () => {
    const response = await fetch(`${API_BASE_URL}/tags`);
    return response.json();
  },

  // 搜索书签
  searchBookmarks: async (query) => {
    const response = await fetch(`${API_BASE_URL}/search?q=${encodeURIComponent(query)}`);
    return response.json();
  },
};

const App = () => {
  const [selectedTag, setSelectedTag] = useState("all");
  const [viewMode, setViewMode] = useState("card");
  const [sortBy, setSortBy] = useState("time");
  const [searchQuery, setSearchQuery] = useState("");
  const [editDrawerVisible, setEditDrawerVisible] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [bookmarks, setBookmarks] = useState([]);
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, total: 0, limit: 20 });

  // 加载书签数据
  const loadBookmarks = async (params = {}) => {
    setLoading(true);
    try {
      const response = await api.getBookmarks({
        tag: selectedTag,
        search: searchQuery,
        sort: sortBy,
        page: pagination.page,
        limit: pagination.limit,
        ...params,
      });
      
      if (response.success) {
        setBookmarks(response.data.bookmarks || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0,
          page: response.data.page || 1,
        }));
      } else {
        message.error(response.error || '加载书签失败');
      }
    } catch (error) {
      message.error('网络错误，请检查连接');
      console.error('Load bookmarks error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载标签数据
  const loadTags = async () => {
    try {
      const response = await api.getTags();
      if (response.success) {
        setTags(response.data || []);
      }
    } catch (error) {
      console.error('Load tags error:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadBookmarks();
    loadTags();
  }, [selectedTag, sortBy, searchQuery]);

  // 创建或更新书签
  const handleSaveBookmark = async (data) => {
    try {
      let response;
      if (editingItem) {
        response = await api.updateBookmark(editingItem.id, data);
      } else {
        response = await api.createBookmark(data);
      }

      if (response.success) {
        message.success(editingItem ? '书签更新成功' : '书签创建成功');
        setEditDrawerVisible(false);
        setEditingItem(null);
        loadBookmarks();
        loadTags();
      } else {
        message.error(response.error || '操作失败');
      }
    } catch (error) {
      message.error('网络错误，请检查连接');
      console.error('Save bookmark error:', error);
    }
  };

  // 删除书签
  const handleDeleteBookmark = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个书签吗？',
      onOk: async () => {
        try {
          const response = await api.deleteBookmark(id);
          if (response.success) {
            message.success('书签删除成功');
            loadBookmarks();
            loadTags();
          } else {
            message.error(response.error || '删除失败');
          }
        } catch (error) {
          message.error('网络错误，请检查连接');
          console.error('Delete bookmark error:', error);
        }
      },
    });
  };

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-orange-400";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-400";
    }
  };

  const getUrgencyText = (urgency) => {
    switch (urgency) {
      case "high":
        return "高";
      case "medium":
        return "中";
      case "low":
        return "低";
      default:
        return "";
    }
  };

  const handleEditItem = (item) => {
    setEditingItem(item);
    setEditDrawerVisible(true);
  };

  const handleAddNew = () => {
    setEditingItem(null);
    setEditDrawerVisible(true);
  };

  const untaggedCount = bookmarks.filter(
    (item) => !item.tags || item.tags.length === 0,
  ).length;

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航栏 */}
      <header className="h-16 bg-yellow-50 border-b border-gray-200 flex items-center justify-between px-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold text-gray-800">标签管理器</h1>
        </div>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Input
              placeholder="搜索标签或网址..."
              prefix={<SearchOutlined className="text-gray-400" />}
              className="w-80"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onPressEnter={() => loadBookmarks()}
            />
          </div>
          <div className="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center cursor-pointer">
            <span className="text-sm font-medium text-gray-700">U</span>
          </div>
        </div>
      </header>

      <div className="flex h-screen">
        {/* 左侧标签分类面板 */}
        <aside className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-800">我的标签</h2>
              <Button
                type="text"
                icon={<PlusOutlined />}
                size="small"
                onClick={handleAddNew}
              >
                新建
              </Button>
            </div>

            <div className="space-y-2">
              <div
                className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedTag === "all"
                    ? "bg-yellow-100 text-yellow-800"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => setSelectedTag("all")}
              >
                <div className="flex items-center space-x-3">
                  <AppstoreOutlined className="text-gray-500" />
                  <span>全部标签</span>
                </div>
                <span className="text-sm text-gray-500">
                  {pagination.total}
                </span>
              </div>

              <div
                className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedTag === "untagged"
                    ? "bg-yellow-100 text-yellow-800"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => setSelectedTag("untagged")}
              >
                <div className="flex items-center space-x-3">
                  <TagOutlined className="text-gray-500" />
                  <span>未分类标签</span>
                </div>
                <span className="text-sm text-gray-500">{untaggedCount}</span>
              </div>

              {tags.map((tag) => (
                <div
                  key={tag.name}
                  className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedTag === tag.name
                      ? "bg-yellow-100 text-yellow-800"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => setSelectedTag(tag.name)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <span>{tag.name}</span>
                  </div>
                  <span className="text-sm text-gray-500">{tag.count}</span>
                </div>
              ))}
            </div>
          </div>
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 overflow-y-auto">
          {/* 工具栏 */}
          <div className="h-16 bg-yellow-50 border-b border-gray-200 flex items-center justify-between px-6">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Button
                  type={viewMode === "card" ? "primary" : "default"}
                  icon={<AppstoreOutlined />}
                  size="small"
                  onClick={() => setViewMode("card")}
                >
                  卡片
                </Button>
                <Button
                  type={viewMode === "list" ? "primary" : "default"}
                  icon={<BarsOutlined />}
                  size="small"
                  onClick={() => setViewMode("list")}
                >
                  列表
                </Button>
              </div>

              <Select
                value={sortBy}
                onChange={setSortBy}
                size="small"
                className="w-32"
                options={[
                  { label: "按时间", value: "time" },
                  { label: "按重要度", value: "importance" },
                  { label: "按紧迫度", value: "urgency" },
                ]}
              />
            </div>

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddNew}
            >
              添加网址
            </Button>
          </div>

          {/* 网址卡片展示区 */}
          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">加载中...</div>
            ) : viewMode === "card" ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {bookmarks.map((item) => (
                  <div
                    key={item.id}
                    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3 flex-1">
                        <img
                          src={item.favicon || "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMTYgMjJDMTkuMzEzNyAyMiAyMiAxOS4zMTM3IDIyIDE2QzIyIDEyLjY4NjMgMTkuMzEzNyAxMCAxNiAxMEMxMi42ODYzIDEwIDEwIDEyLjY4NjMgMTAgMTZDMTAgMTkuMzEzNyAxMi42ODYzIDIyIDE2IDIyWiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K"}
                          alt=""
                          className="w-8 h-8 rounded object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 truncate">
                            {item.title}
                          </h3>
                          <p className="text-sm text-gray-500 truncate">
                            {item.url}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={() => handleEditItem(item)}
                        />
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          size="small"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDeleteBookmark(item.id)}
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 mb-3">
                      <div className="flex items-center space-x-1">
                        <StarOutlined className="text-yellow-500 text-sm" />
                        <Rate disabled value={item.importance} size="small" />
                      </div>
                      <div className="flex items-center space-x-1">
                        <div
                          className={`w-2 h-2 rounded-full ${getUrgencyColor(item.urgency)}`}
                        ></div>
                        <span className="text-xs text-gray-600">
                          {getUrgencyText(item.urgency)}
                        </span>
                      </div>
                    </div>

                    {item.reminderDate && (
                      <div className="flex items-center space-x-1 mb-3">
                        <ClockCircleOutlined className="text-gray-400 text-sm" />
                        <span className="text-xs text-gray-600">
                          {item.reminderDate}
                        </span>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-1">
                      {item.tags && item.tags.length > 0 ? (
                        item.tags.map((tag) => (
                          <Tag key={tag} color="gold" className="text-xs">
                            {tag}
                          </Tag>
                        ))
                      ) : (
                        <Tag color="default" className="text-xs">
                          未分类
                        </Tag>
                      )}
                    </div>

                    {item.notes && (
                      <p className="text-xs text-gray-500 mt-2 line-clamp-2">
                        {item.notes}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {bookmarks.map((item) => (
                  <div
                    key={item.id}
                    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 flex-1">
                        <img
                          src={item.favicon || "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iMyIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMTIgMTZDMTQuMjA5MSAxNiAxNiAxNC4yMDkxIDE2IDEyQzE2IDkuNzkwODYgMTQuMjA5MSA4IDEyIDhDOS43OTA4NiA4IDggOS43OTA4NiA4IDEyQzggMTQuMjA5MSA5Ljc5MDg2IDE2IDEyIDE2WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K"}
                          alt=""
                          className="w-6 h-6 rounded object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900">
                            {item.title}
                          </h3>
                          <p className="text-sm text-gray-500">{item.url}</p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1">
                            <Rate
                              disabled
                              value={item.importance}
                              size="small"
                            />
                          </div>
                          <div className="flex items-center space-x-1">
                            <div
                              className={`w-2 h-2 rounded-full ${getUrgencyColor(item.urgency)}`}
                            ></div>
                            <span className="text-xs text-gray-600">
                              {getUrgencyText(item.urgency)}
                            </span>
                          </div>
                          {item.reminderDate && (
                            <div className="flex items-center space-x-1">
                              <ClockCircleOutlined className="text-gray-400 text-sm" />
                              <span className="text-xs text-gray-600">
                                {item.reminderDate}
                              </span>
                            </div>
                          )}
                          <div className="flex flex-wrap gap-1">
                            {item.tags && item.tags.length > 0 ? (
                              item.tags.slice(0, 2).map((tag) => (
                                <Tag key={tag} color="gold" className="text-xs">
                                  {tag}
                                </Tag>
                              ))
                            ) : (
                              <Tag color="default" className="text-xs">
                                未分类
                              </Tag>
                            )}
                            {item.tags && item.tags.length > 2 && (
                              <Tag color="default" className="text-xs">
                                +{item.tags.length - 2}
                              </Tag>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={() => handleEditItem(item)}
                        />
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          size="small"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDeleteBookmark(item.id)}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 编辑抽屉 */}
      <BookmarkDrawer
        visible={editDrawerVisible}
        onClose={() => {
          setEditDrawerVisible(false);
          setEditingItem(null);
        }}
        onSave={handleSaveBookmark}
        editingItem={editingItem}
        allTags={tags.map(tag => tag.name)}
      />
    </div>
  );
};

// 书签编辑抽屉组件
const BookmarkDrawer = ({ visible, onClose, onSave, editingItem, allTags }) => {
  const [form, setForm] = useState({
    title: '',
    url: '',
    tags: [],
    importance: 1,
    urgency: 'low',
    reminderDate: '',
    notes: '',
    favicon: '',
  });

  useEffect(() => {
    if (editingItem) {
      setForm({
        title: editingItem.title || '',
        url: editingItem.url || '',
        tags: editingItem.tags || [],
        importance: editingItem.importance || 1,
        urgency: editingItem.urgency || 'low',
        reminderDate: editingItem.reminderDate || '',
        notes: editingItem.notes || '',
        favicon: editingItem.favicon || '',
      });
    } else {
      setForm({
        title: '',
        url: '',
        tags: [],
        importance: 1,
        urgency: 'low',
        reminderDate: '',
        notes: '',
        favicon: '',
      });
    }
  }, [editingItem, visible]);

  const handleSave = () => {
    if (!form.title || !form.url) {
      message.error('标题和URL是必需的');
      return;
    }
    onSave(form);
  };

  return (
    <Drawer
      title={editingItem ? "编辑书签" : "添加书签"}
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      extra={
        <div className="flex space-x-2">
          <Button onClick={onClose}>
            取消
          </Button>
          <Button type="primary" onClick={handleSave}>
            保存
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            标题 *
          </label>
          <Input
            value={form.title}
            onChange={(e) => setForm({ ...form, title: e.target.value })}
            placeholder="请输入标题"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            URL *
          </label>
          <Input
            value={form.url}
            onChange={(e) => setForm({ ...form, url: e.target.value })}
            placeholder="请输入网址"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            标签
          </label>
          <Select
            mode="tags"
            value={form.tags}
            onChange={(tags) => setForm({ ...form, tags })}
            className="w-full"
            placeholder="选择或创建标签"
            options={allTags.map((tag) => ({ label: tag, value: tag }))}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            重要度
          </label>
          <Rate 
            value={form.importance} 
            onChange={(importance) => setForm({ ...form, importance })}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            紧迫度
          </label>
          <Select
            value={form.urgency}
            onChange={(urgency) => setForm({ ...form, urgency })}
            className="w-full"
            options={[
              { label: "高", value: "high" },
              { label: "中", value: "medium" },
              { label: "低", value: "low" },
            ]}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            提醒日期
          </label>
          <Input
            type="date"
            value={form.reminderDate}
            onChange={(e) => setForm({ ...form, reminderDate: e.target.value })}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            图标URL
          </label>
          <Input
            value={form.favicon}
            onChange={(e) => setForm({ ...form, favicon: e.target.value })}
            placeholder="请输入图标URL"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            备注
          </label>
          <Input.TextArea
            value={form.notes}
            onChange={(e) => setForm({ ...form, notes: e.target.value })}
            rows={4}
            placeholder="添加备注信息"
          />
        </div>
      </div>
    </Drawer>
  );
};

export default App;

