# 书签管理器后端API

这是一个基于Flask和MongoDB的书签管理器后端API，为React前端应用提供数据服务。

## 功能特性

- 书签的增删改查操作
- 标签管理和分类
- 搜索和排序功能
- 重要度和紧迫度管理
- 提醒日期设置
- 备注功能

## 技术栈

- **框架**: Flask 3.1.1
- **数据库**: MongoDB
- **CORS**: Flask-CORS
- **数据库驱动**: PyMongo
- **环境变量**: python-dotenv

## 安装和运行

### 1. 安装依赖

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件（可选）：

```env
SECRET_KEY=your_secret_key
MONGODB_URI=********************************************
MONGODB_DB=bookmark_manager
```

### 3. 运行服务器

```bash
python src/main.py
```

服务器将在 `http://0.0.0.0:5000` 启动。

## API接口文档

### 健康检查

- **GET** `/api/health` - 检查服务器和数据库连接状态

### 书签管理

#### 获取书签列表
- **GET** `/api/bookmarks`
- **查询参数**:
  - `tag`: 标签过滤 (可选)
  - `search`: 搜索关键词 (可选)
  - `sort`: 排序方式 (time/importance/urgency)
  - `page`: 页码 (默认1)
  - `limit`: 每页数量 (默认20)

#### 创建书签
- **POST** `/api/bookmarks`
- **请求体**:
```json
{
  "title": "网站标题",
  "url": "https://example.com",
  "tags": ["标签1", "标签2"],
  "importance": 5,
  "urgency": "high",
  "reminderDate": "2025-01-15",
  "notes": "备注信息",
  "favicon": "图标URL"
}
```

#### 获取单个书签
- **GET** `/api/bookmarks/{id}`

#### 更新书签
- **PUT** `/api/bookmarks/{id}`
- **请求体**: 同创建书签

#### 删除书签
- **DELETE** `/api/bookmarks/{id}`

### 标签管理

#### 获取所有标签
- **GET** `/api/tags`

### 搜索

#### 搜索书签
- **GET** `/api/search`
- **查询参数**:
  - `q`: 搜索关键词
  - `type`: 搜索类型 (可选)

## 数据模型

### BookmarkItem
```json
{
  "id": "ObjectId字符串",
  "title": "网站标题",
  "url": "网站URL",
  "tags": ["标签数组"],
  "importance": "重要度(1-5)",
  "urgency": "紧迫度(high/medium/low)",
  "reminderDate": "提醒日期(ISO格式)",
  "notes": "备注",
  "favicon": "图标URL",
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

## 响应格式

所有API响应都使用统一格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "error": null
}
```

## 错误处理

- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 部署说明

1. 确保MongoDB数据库可访问
2. 设置正确的环境变量
3. 运行 `python src/main.py`
4. 服务器监听在 `0.0.0.0:5000`

## 开发说明

- 使用虚拟环境进行开发
- 修改代码后重启服务器
- 数据库连接使用单例模式
- 支持CORS跨域请求

