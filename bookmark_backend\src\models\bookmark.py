from datetime import datetime
from bson import ObjectId
from src.utils.database import get_db
from src.utils.helpers import serialize_doc

class BookmarkModel:
    def __init__(self):
        self.db = get_db()
        self.collection = self.db.bookmarks
    
    def create_bookmark(self, data):
        """创建新书签"""
        bookmark = {
            "title": data.get("title", ""),
            "url": data.get("url", ""),
            "tags": data.get("tags", []),
            "importance": data.get("importance", 1),
            "urgency": data.get("urgency", "low"),
            "reminderDate": data.get("reminderDate"),
            "notes": data.get("notes", ""),
            "favicon": data.get("favicon", ""),
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        }
        
        result = self.collection.insert_one(bookmark)
        bookmark["_id"] = result.inserted_id
        return serialize_doc(bookmark)
    
    def get_bookmarks(self, filters=None, sort_by="createdAt", page=1, limit=20):
        """获取书签列表"""
        query = {}
        
        if filters:
            if filters.get("tag"):
                if filters["tag"] == "untagged":
                    query["tags"] = {"$size": 0}
                else:
                    query["tags"] = {"$in": [filters["tag"]]}
            
            if filters.get("search"):
                search_term = filters["search"]
                query["$or"] = [
                    {"title": {"$regex": search_term, "$options": "i"}},
                    {"url": {"$regex": search_term, "$options": "i"}},
                    {"notes": {"$regex": search_term, "$options": "i"}},
                    {"tags": {"$regex": search_term, "$options": "i"}}
                ]
        
        # 排序
        sort_field = "createdAt"
        sort_direction = -1  # 降序
        
        if sort_by == "importance":
            sort_field = "importance"
        elif sort_by == "urgency":
            sort_field = "urgency"
        elif sort_by == "time":
            sort_field = "createdAt"
        
        skip = (page - 1) * limit
        
        cursor = self.collection.find(query).sort(sort_field, sort_direction).skip(skip).limit(limit)
        bookmarks = list(cursor)
        total = self.collection.count_documents(query)
        
        return {
            "bookmarks": serialize_doc(bookmarks),
            "total": total,
            "page": page,
            "limit": limit,
            "pages": (total + limit - 1) // limit
        }
    
    def get_bookmark_by_id(self, bookmark_id):
        """根据ID获取书签"""
        try:
            bookmark = self.collection.find_one({"_id": ObjectId(bookmark_id)})
            return serialize_doc(bookmark)
        except:
            return None
    
    def update_bookmark(self, bookmark_id, data):
        """更新书签"""
        try:
            update_data = {
                "title": data.get("title"),
                "url": data.get("url"),
                "tags": data.get("tags"),
                "importance": data.get("importance"),
                "urgency": data.get("urgency"),
                "reminderDate": data.get("reminderDate"),
                "notes": data.get("notes"),
                "favicon": data.get("favicon"),
                "updatedAt": datetime.utcnow()
            }
            
            # 移除None值
            update_data = {k: v for k, v in update_data.items() if v is not None}
            
            result = self.collection.update_one(
                {"_id": ObjectId(bookmark_id)},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                return self.get_bookmark_by_id(bookmark_id)
            return None
        except:
            return None
    
    def delete_bookmark(self, bookmark_id):
        """删除书签"""
        try:
            result = self.collection.delete_one({"_id": ObjectId(bookmark_id)})
            return result.deleted_count > 0
        except:
            return False
    
    def get_all_tags(self):
        """获取所有标签及其统计"""
        pipeline = [
            {"$unwind": "$tags"},
            {"$group": {"_id": "$tags", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        tags = list(self.collection.aggregate(pipeline))
        return [{"name": tag["_id"], "count": tag["count"]} for tag in tags]

