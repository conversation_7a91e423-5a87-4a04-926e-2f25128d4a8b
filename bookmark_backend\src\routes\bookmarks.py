from flask import Blueprint, request, jsonify
from src.models.bookmark import BookmarkModel
from src.utils.helpers import create_response, validate_object_id

bookmarks_bp = Blueprint('bookmarks', __name__)
bookmark_model = BookmarkModel()

@bookmarks_bp.route('/bookmarks', methods=['GET'])
def get_bookmarks():
    """获取书签列表"""
    try:
        # 获取查询参数
        tag = request.args.get('tag', 'all')
        search = request.args.get('search', '')
        sort_by = request.args.get('sort', 'time')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        
        # 构建过滤条件
        filters = {}
        if tag != 'all':
            filters['tag'] = tag
        if search:
            filters['search'] = search
        
        # 获取书签数据
        result = bookmark_model.get_bookmarks(filters, sort_by, page, limit)
        
        return jsonify(create_response(
            success=True,
            data=result,
            message="获取书签列表成功"
        ))
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

@bookmarks_bp.route('/bookmarks', methods=['POST'])
def create_bookmark():
    """创建新书签"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        if not data.get('title') or not data.get('url'):
            return jsonify(create_response(
                success=False,
                error="标题和URL是必需的"
            )), 400
        
        # 创建书签
        bookmark = bookmark_model.create_bookmark(data)
        
        return jsonify(create_response(
            success=True,
            data=bookmark,
            message="书签创建成功"
        )), 201
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

@bookmarks_bp.route('/bookmarks/<bookmark_id>', methods=['GET'])
def get_bookmark(bookmark_id):
    """获取单个书签"""
    try:
        if not validate_object_id(bookmark_id):
            return jsonify(create_response(
                success=False,
                error="无效的书签ID"
            )), 400
        
        bookmark = bookmark_model.get_bookmark_by_id(bookmark_id)
        
        if not bookmark:
            return jsonify(create_response(
                success=False,
                error="书签不存在"
            )), 404
        
        return jsonify(create_response(
            success=True,
            data=bookmark,
            message="获取书签成功"
        ))
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

@bookmarks_bp.route('/bookmarks/<bookmark_id>', methods=['PUT'])
def update_bookmark(bookmark_id):
    """更新书签"""
    try:
        if not validate_object_id(bookmark_id):
            return jsonify(create_response(
                success=False,
                error="无效的书签ID"
            )), 400
        
        data = request.get_json()
        bookmark = bookmark_model.update_bookmark(bookmark_id, data)
        
        if not bookmark:
            return jsonify(create_response(
                success=False,
                error="书签不存在或更新失败"
            )), 404
        
        return jsonify(create_response(
            success=True,
            data=bookmark,
            message="书签更新成功"
        ))
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

@bookmarks_bp.route('/bookmarks/<bookmark_id>', methods=['DELETE'])
def delete_bookmark(bookmark_id):
    """删除书签"""
    try:
        if not validate_object_id(bookmark_id):
            return jsonify(create_response(
                success=False,
                error="无效的书签ID"
            )), 400
        
        success = bookmark_model.delete_bookmark(bookmark_id)
        
        if not success:
            return jsonify(create_response(
                success=False,
                error="书签不存在或删除失败"
            )), 404
        
        return jsonify(create_response(
            success=True,
            message="书签删除成功"
        ))
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

@bookmarks_bp.route('/tags', methods=['GET'])
def get_tags():
    """获取所有标签"""
    try:
        tags = bookmark_model.get_all_tags()
        
        return jsonify(create_response(
            success=True,
            data=tags,
            message="获取标签列表成功"
        ))
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

@bookmarks_bp.route('/search', methods=['GET'])
def search_bookmarks():
    """搜索书签"""
    try:
        query = request.args.get('q', '')
        search_type = request.args.get('type', 'all')
        
        if not query:
            return jsonify(create_response(
                success=False,
                error="搜索关键词不能为空"
            )), 400
        
        filters = {'search': query}
        result = bookmark_model.get_bookmarks(filters)
        
        return jsonify(create_response(
            success=True,
            data=result,
            message="搜索完成"
        ))
        
    except Exception as e:
        return jsonify(create_response(
            success=False,
            error=str(e)
        )), 500

