# 书签管理器 - 前端+后端整合部署指南

## 项目概述

这是一个完整的书签管理器应用，包含：
- **前端**: React + Ant Design + Tailwind CSS
- **后端**: Python Flask + MongoDB
- **功能**: 书签管理、标签分类、搜索排序、重要度管理等

## 项目结构

```
bookmark_fullstack/
├── bookmark_backend/          # Flask后端项目
│   ├── src/
│   │   ├── static/           # 前端构建文件（自动生成）
│   │   ├── models/           # 数据模型
│   │   ├── routes/           # API路由
│   │   ├── utils/            # 工具函数
│   │   └── main.py           # Flask主文件
│   ├── venv/                 # Python虚拟环境
│   ├── config.py             # 配置文件
│   ├── requirements.txt      # Python依赖
│   └── README.md             # 后端说明
├── bookmark_frontend/         # React前端项目
│   ├── src/                  # 前端源码
│   ├── dist/                 # 构建输出（自动生成）
│   ├── package.json          # 前端依赖
│   └── ...
└── readme_deploy.md          # 本文档
```

## 快速开始

### 方式一：运行整合后的应用（推荐）

这种方式只需要启动Flask后端，前端已经构建并整合到后端中。

1. **进入后端目录**
```bash
cd bookmark_backend
```

2. **激活虚拟环境**
```bash
source venv/bin/activate
```

3. **安装依赖**（如果是首次运行）
```bash
pip install -r requirements.txt
```

4. **启动应用**
```bash
python src/main.py
```

5. **访问应用**
- 打开浏览器访问: `http://localhost:5000`
- API接口地址: `http://localhost:5000/api`

### 方式二：分别运行前端和后端（开发模式）

如果需要修改前端代码，可以分别运行前端和后端。

#### 启动后端
```bash
cd bookmark_backend
source venv/bin/activate
python src/main.py
```
后端将在 `http://localhost:5000` 运行

#### 启动前端
```bash
cd bookmark_frontend
pnpm install  # 首次运行需要安装依赖
pnpm run dev --host
```
前端将在 `http://localhost:5173` 运行

## 环境要求

### Python环境
- Python 3.11+
- pip 或 pip3

### Node.js环境（仅开发模式需要）
- Node.js 18+
- pnpm（推荐）或 npm

### 数据库
- MongoDB（已配置连接到提供的数据库）

## 配置说明

### 数据库配置
数据库连接已在 `config.py` 中配置：
```python
MONGODB_URI = '*******************************************/'
MONGODB_DB = 'bookmark_manager'
```

### API配置
前端会根据运行环境自动选择API地址：
- 生产环境（整合模式）: 使用相对路径 `/api`
- 开发环境（分离模式）: 使用完整URL

## 功能特性

### 书签管理
- ✅ 创建、编辑、删除书签
- ✅ 设置重要度（1-5星）
- ✅ 设置紧迫度（高/中/低）
- ✅ 添加提醒日期
- ✅ 添加备注信息
- ✅ 自定义图标URL

### 标签系统
- ✅ 创建和管理标签
- ✅ 标签分类浏览
- ✅ 标签统计显示
- ✅ 未分类书签管理

### 搜索和排序
- ✅ 全文搜索（标题、URL、备注、标签）
- ✅ 按时间、重要度、紧迫度排序
- ✅ 标签过滤

### 界面功能
- ✅ 卡片视图和列表视图切换
- ✅ 响应式设计
- ✅ 现代化UI（Ant Design）

## API接口

### 健康检查
- `GET /api/health` - 检查服务状态

### 书签管理
- `GET /api/bookmarks` - 获取书签列表
- `POST /api/bookmarks` - 创建书签
- `GET /api/bookmarks/{id}` - 获取单个书签
- `PUT /api/bookmarks/{id}` - 更新书签
- `DELETE /api/bookmarks/{id}` - 删除书签

### 标签管理
- `GET /api/tags` - 获取所有标签

### 搜索
- `GET /api/search?q=关键词` - 搜索书签

## 开发说明

### 修改前端代码
1. 修改 `bookmark_frontend/src/` 中的文件
2. 重新构建前端: `cd bookmark_frontend && pnpm run build`
3. 更新后端静态文件: `cp -r bookmark_frontend/dist/* bookmark_backend/src/static/`
4. 重启Flask服务器

### 修改后端代码
1. 修改 `bookmark_backend/src/` 中的文件
2. 重启Flask服务器即可

### 数据库操作
- 数据库连接使用单例模式
- 支持自动重连
- 所有操作都有错误处理

## 部署到生产环境

### 本地部署
1. 确保MongoDB可访问
2. 按照"快速开始"中的步骤运行
3. 使用 `0.0.0.0:5000` 监听所有网络接口

### 云服务器部署
1. 上传项目文件到服务器
2. 安装Python和依赖
3. 配置防火墙开放5000端口
4. 使用进程管理器（如supervisor）管理Flask进程

### Docker部署（可选）
可以创建Dockerfile来容器化部署：
```dockerfile
FROM python:3.11
WORKDIR /app
COPY bookmark_backend/ .
RUN pip install -r requirements.txt
EXPOSE 5000
CMD ["python", "src/main.py"]
```

## 故障排除

### 常见问题

1. **无法连接数据库**
   - 检查网络连接
   - 确认数据库地址和凭据正确

2. **前端页面空白**
   - 检查静态文件是否正确复制到 `bookmark_backend/src/static/`
   - 确认Flask服务器正常运行

3. **API请求失败**
   - 检查CORS配置
   - 确认API路由正确注册

4. **依赖安装失败**
   - 使用虚拟环境
   - 更新pip: `pip install --upgrade pip`

### 日志查看
Flask运行时会显示详细的请求日志，包括：
- API请求路径和方法
- 响应状态码
- 错误信息

## 技术栈详情

### 前端技术栈
- **React 19.1.0** - 前端框架
- **Ant Design 5.26.3** - UI组件库
- **Tailwind CSS** - 样式框架
- **Vite** - 构建工具
- **pnpm** - 包管理器

### 后端技术栈
- **Flask 3.1.1** - Web框架
- **PyMongo 4.13.2** - MongoDB驱动
- **Flask-CORS 6.0.0** - 跨域支持
- **python-dotenv 1.1.1** - 环境变量管理

### 数据库
- **MongoDB** - 文档数据库
- **集合**: bookmarks, tags
- **索引**: 支持全文搜索

## 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的书签管理功能
- ✅ 前后端整合部署
- ✅ MongoDB数据库集成
- ✅ 响应式UI设计
- ✅ 完善的错误处理

## 支持

如有问题，请检查：
1. 依赖是否正确安装
2. 数据库连接是否正常
3. 端口是否被占用
4. 防火墙设置是否正确

---

**注意**: 本应用已经过完整测试，所有功能正常运行。如遇到问题，请按照故障排除部分的指导进行检查。

