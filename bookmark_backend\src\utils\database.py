from pymongo import MongoClient
from config import Config

class Database:
    _instance = None
    _client = None
    _db = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
        return cls._instance
    
    def connect(self):
        if self._client is None:
            self._client = MongoClient(Config.MONGODB_URI)
            self._db = self._client[Config.MONGODB_DB]
        return self._db
    
    def get_db(self):
        if self._db is None:
            self.connect()
        return self._db
    
    def close(self):
        if self._client:
            self._client.close()
            self._client = None
            self._db = None

# 全局数据库实例
db_instance = Database()

def get_db():
    return db_instance.get_db()

