import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS
from config import Config
from src.routes.bookmarks import bookmarks_bp
from src.utils.database import get_db
from src.utils.helpers import create_response

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
app.config.from_object(Config)

# 启用CORS
CORS(app, origins=Config.CORS_ORIGINS)

# 注册蓝图
app.register_blueprint(bookmarks_bp, url_prefix='/api')

# 测试数据库连接
@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        db = get_db()
        # 测试数据库连接
        db.command('ping')
        return jsonify(create_response(
            success=True,
            data={"status": "healthy", "database": "connected"},
            message="服务运行正常"
        ))
    except Exception as e:
        return jsonify(create_response(
            success=False,
            data={"status": "unhealthy", "database": "disconnected"},
            error=str(e)
        )), 500

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    """服务静态文件"""
    static_folder_path = app.static_folder
    if static_folder_path is None:
        return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return jsonify(create_response(
                success=True,
                data={"message": "Bookmark Manager API Server"},
                message="API服务器运行中"
            ))

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify(create_response(
        success=False,
        error="资源不存在"
    )), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify(create_response(
        success=False,
        error="服务器内部错误"
    )), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

