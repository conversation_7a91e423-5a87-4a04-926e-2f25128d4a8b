from bson import ObjectId
from datetime import datetime
import json

def serialize_doc(doc):
    """序列化MongoDB文档，处理ObjectId和datetime"""
    if doc is None:
        return None
    
    if isinstance(doc, list):
        return [serialize_doc(item) for item in doc]
    
    if isinstance(doc, dict):
        result = {}
        for key, value in doc.items():
            if key == '_id' and isinstance(value, ObjectId):
                result['id'] = str(value)
            elif isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, dict):
                result[key] = serialize_doc(value)
            elif isinstance(value, list):
                result[key] = serialize_doc(value)
            else:
                result[key] = value
        return result
    
    return doc

def create_response(success=True, data=None, message="", error=None):
    """创建标准API响应格式"""
    return {
        "success": success,
        "data": data,
        "message": message,
        "error": error
    }

def validate_object_id(id_string):
    """验证ObjectId格式"""
    try:
        ObjectId(id_string)
        return True
    except:
        return False

